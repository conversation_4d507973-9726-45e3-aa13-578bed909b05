package cn.iocoder.yudao.module.mall.product.enums.category;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 商品分类 状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProductCategoryStatusEnum implements IntArrayValuable {

    DISABLE(0, "停用"),
    ENABLE(1, "启用");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ProductCategoryStatusEnum::getStatus).toArray();

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static ProductCategoryStatusEnum fromStatus(int status) {
        if(DISABLE.status.equals(status)) {
            return DISABLE;
        } else if(ENABLE.status.equals(status)) {
            return ENABLE;
        }

        return null;
    }

    /**
     * 判断是否处于【上架】状态
     *
     * @param status 状态
     * @return 是否处于【上架】状态
     */
    public static boolean isEnable(Integer status) {
        return ENABLE.getStatus().equals(status);
    }

}
