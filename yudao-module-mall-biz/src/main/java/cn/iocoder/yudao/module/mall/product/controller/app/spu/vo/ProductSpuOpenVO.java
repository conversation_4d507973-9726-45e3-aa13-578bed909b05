package cn.iocoder.yudao.module.mall.product.controller.app.spu.vo;

import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.AppProductSkuOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spec.vo.AppProductSpecValueVO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品spu信息
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Data
public class ProductSpuOpenVO extends ProductSpuBaseOpenVO {

    /**
     * spu规格值
     */
    @Valid
    private List<AppProductSpecValueVO> spuSpecValueList;

    /**
     * sku信息
     */
    @NotNull(message = "skus不能为空")
    @Valid
    private List<AppProductSkuOpenVO> skus;
}
