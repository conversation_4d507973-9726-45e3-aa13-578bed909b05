package cn.iocoder.yudao.module.mall.product.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品分类 Response VO")
@Data
@ToString(callSuper = true)
public class ProductCategorySimpleRespVO {

    @Schema(description = "分类名称")
    @NotNull(message = "分类名称不能为空")
    private String categoryName;

    @Schema(description = "父级分类ID")
    @NotNull(message = "父分类编号不能为空")
    private Long parentId;

    @Schema(description = "商品分类编号, 以-分隔")
    private String fullCategoryId;

    @Schema(description = "商品分类名称, 以-分隔")
    private String fullCategoryName;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类级别")
    private Integer categoryLevel;

    @Schema(description = "分类状态")
    private Integer status;

}
