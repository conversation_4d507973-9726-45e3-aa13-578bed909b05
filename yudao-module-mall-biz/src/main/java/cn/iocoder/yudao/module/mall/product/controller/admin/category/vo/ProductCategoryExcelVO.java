package cn.iocoder.yudao.module.mall.product.controller.admin.category.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ProductCategoryExcelVO
{
    @ExcelProperty(index = 0, value = "一级分类ID")
    private String category1Id;

    @ExcelProperty(index = 1, value = "一级分类")
    private String category1Name;

    @ExcelProperty(index = 2, value = "二级分类ID")
    private String category2Id;

    @ExcelProperty(index = 3, value = "二级分类")
    private String category2Name;

    @ExcelProperty(index = 4, value = "三级分类ID")
    private String category3Id;

    @ExcelProperty(index = 5, value = "三级分类")
    private String category3Name;
}
