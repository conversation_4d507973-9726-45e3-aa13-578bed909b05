<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.basis.dal.mysql.stats.StatsUserMapper">


    <select id="getTotalLoginUserStats"
            parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserStatsReqVO"
            resultType="java.lang.Long">
        select count(*) from member_user t1
        <where>
        t1.deleted = 0
        and t1.tenant_id = #{params.tenantId}
        <if test="params.startDate != null and params.startDate != ''">
            and t1.login_date <![CDATA[>=]]> #{params.startDate}
        </if>
        <if test="params.endDate != null and params.endDate != ''">
            and t1.login_date <![CDATA[<]]> #{params.endDate}
        </if>
        </where>
    </select>

    <select id="getTotalOrderUserStats"
            parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserStatsReqVO"
            resultType="java.lang.Long">
        select count(distinct t1.user_id) from mall_stats_order t1
        <where>
            t1.deleted = 0
            and t1.tenant_id = #{params.tenantId}
            <if test="params.startDate != null and params.startDate != ''">
                and t1.stats_date <![CDATA[>=]]> #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                and t1.stats_date <![CDATA[<=]]> #{params.endDate}
            </if>
        </where>
    </select>

    <select id="getUserTotal"
            parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserStatsReqVO"
            resultType="java.lang.Long">
        select count(*) from member_user t1 where t1.deleted = 0 and t1.tenant_id = #{params.tenantId}
    </select>

    <select id="queryUserOrderStats"
            parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserStatsReqVO"
            resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserOrderStatsRespVO">
        select
            t1.user_id,
            t2.nickname,
            t2.name,
            count(t1.order_id) as orderCount,
            sum(t1.total_price) as totalPrice
        from mall_stats_order t1, member_user t2
        <where>
            t1.deleted = 0
            and t1.tenant_id = #{params.tenantId}
            and t2.deleted = 0
            and t2.id = t1.user_id
            and t2.tenant_id = #{params.tenantId}
            <if test="params.startDate != null and params.startDate != ''">
                and t1.stats_date <![CDATA[>=]]> #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                and t1.stats_date <![CDATA[<=]]> #{params.endDate}
            </if>
        </where>
        group by t1.user_id
        <if test="params.sortType!= null">
            <choose>
                <when test="params.sortType == 10">
                    order by orderCount desc
                </when>
                <when test="params.sortType == 11">
                    order by orderCount
                </when>
                <when test="params.sortType == 20">
                    order by totalPrice desc
                </when>
                <when test="params.sortType == 21">
                    order by totalPrice
                </when>
                <otherwise>
                    order by totalPrice desc
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="getUserOrderTotalStats"
            parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserStatsReqVO"
            resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserOrderStatsRespVO">
        select
            COALESCE(count(t1.order_id),0) as orderCount,
            COALESCE(sum(t1.total_price),0) as totalPrice
        from mall_stats_order t1
        <where>
            t1.deleted = 0
            and t1.tenant_id = #{params.tenantId}
            <if test="params.userId != null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.startDate != null and params.startDate != ''">
                and t1.stats_date <![CDATA[>=]]> #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                and t1.stats_date <![CDATA[<=]]> #{params.endDate}
            </if>
        </where>
    </select>

    <select id="queryUserTotalOrderStats"
            parameterType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserStatsReqVO"
            resultType="cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.user.UserTotalOrderStatsRespVO">
        select
            COALESCE(count(t1.order_id),0) as orderCount,
            COALESCE(sum(t1.total_price),0) as totalPrice
        from mall_stats_order t1
        <where>
            t1.deleted = 0
            and t1.tenant_id = #{params.tenantId}
            <if test="params.userId != null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.deptId != null">
                and t1.dept_id = #{params.deptId}
            </if>
        </where>
    </select>


</mapper>