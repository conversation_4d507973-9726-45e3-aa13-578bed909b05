--- #################### 注册中心相关配置 ####################
spring:
  cloud:
    nacos:
      server-addr: 127.0.0.1:18033
      username: nacos
      password: Jc<PERSON><PERSON><PERSON><PERSON><PERSON>!@rrt
      discovery:
        namespace: a0e141e2-c3c8-4ca8-8286-f4edc1dbb962 # 命名空间。这里使用 dev 开发环境
        metadata:
          version: 1.1.0 # 服务实例的版本号，可用于灰度发布
      config:
        namespace: a0e141e2-c3c8-4ca8-8286-f4edc1dbb962        # 命名空间 dev 的ID，不能直接使用 dev 名称。创建命名空间的时候需要指定ID为 dev，这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        name: ${spring.application.name} # 使用的 Nacos 配置集的 dataId，默认为 spring.application.name
        file-extension: yaml # 使用的 Nacos 配置集的 dataId 的文件拓展名，同时也是 Nacos 配置集的配置格式，默认为 properties

mall:
  open-api:
    oauth-validate: false # 开放API关闭校验
yudao:
  env: # 多环境的配置项
    tag: ${HOSTNAME}